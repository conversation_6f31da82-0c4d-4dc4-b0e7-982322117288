"""
Export Service
==============

Service for exporting data and reports in various formats.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from typing import Dict, Any, Optional, Callable, List
import pandas as pd
import logging
from pathlib import Path
from datetime import datetime
import json

from core.enhanced_financial_model import export_enhanced_excel
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from utils.file_utils import FileUtils


class ExportService:
    """Service for exporting data and generating reports."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.file_utils = FileUtils()
    
    def export_excel_report(self,
                          client_profile: ClientProfile,
                          assumptions: EnhancedProjectAssumptions,
                          financial_results: Dict[str, Any],
                          sensitivity_results: Optional[pd.DataFrame] = None,
                          monte_carlo_results: Optional[Dict[str, Any]] = None,
                          scenario_results: Optional[Dict[str, Any]] = None,
                          output_dir: Optional[Path] = None,
                          progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export comprehensive Excel report."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up Excel export...")
            
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_model_{timestamp}.xlsx"
            filepath = output_dir['data_dir'] / filename
            
            if progress_callback:
                progress_callback(30, "Preparing data for export...")
            
            # Prepare data for export
            scenarios_dict = scenario_results if scenario_results else {}
            sensitivity_df = sensitivity_results if sensitivity_results is not None else pd.DataFrame()
            mc_stats = monte_carlo_results.get('statistics', {}) if monte_carlo_results else {}
            
            if progress_callback:
                progress_callback(60, "Writing Excel file...")
            
            # Export using core function
            export_enhanced_excel(
                scenarios=scenarios_dict,
                sensitivity_df=sensitivity_df,
                mc_stats=mc_stats,
                filename=str(filepath)
            )
            
            if progress_callback:
                progress_callback(90, "Finalizing export...")
            
            # Add metadata sheet
            self._add_metadata_sheet(filepath, client_profile, assumptions)
            
            if progress_callback:
                progress_callback(100, "Excel export completed")
            
            self.logger.info(f"Excel report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting Excel report: {str(e)}")
            raise
    
    def export_docx_report(self,
                         client_profile: ClientProfile,
                         assumptions: EnhancedProjectAssumptions,
                         financial_results: Dict[str, Any],
                         validation_results: Optional[Any] = None,
                         charts: Optional[Dict[str, bytes]] = None,
                         output_dir: Optional[Path] = None,
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export DOCX report with charts."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up DOCX export...")
            
            # Import DOCX functionality
            try:
                from docx import Document
                from docx.shared import Inches, Pt
                from docx.enum.text import WD_ALIGN_PARAGRAPH
            except ImportError:
                raise ImportError("python-docx is required for DOCX export")
            
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_report_{timestamp}.docx"
            filepath = output_dir['reports_dir'] / filename
            
            if progress_callback:
                progress_callback(30, "Creating DOCX document...")
            
            # Create document
            doc = Document()
            
            # Add title and client info
            self._add_docx_header(doc, client_profile, assumptions)
            
            if progress_callback:
                progress_callback(50, "Adding financial results...")
            
            # Add financial results
            self._add_docx_financial_results(doc, financial_results)
            
            if progress_callback:
                progress_callback(70, "Adding charts...")
            
            # Add charts if provided
            if charts:
                self._add_docx_charts(doc, charts)
            
            if progress_callback:
                progress_callback(90, "Adding validation results...")
            
            # Add validation results
            if validation_results:
                self._add_docx_validation_results(doc, validation_results)
            
            # Save document
            doc.save(str(filepath))
            
            if progress_callback:
                progress_callback(100, "DOCX export completed")
            
            self.logger.info(f"DOCX report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting DOCX report: {str(e)}")
            raise
    
    def export_html_report(self,
                         client_profile: ClientProfile,
                         assumptions: EnhancedProjectAssumptions,
                         financial_results: Dict[str, Any],
                         output_dir: Optional[Path] = None,
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Path:
        """Export HTML report."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up HTML export...")
            
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_report_{timestamp}.html"
            filepath = output_dir['reports_dir'] / filename
            
            if progress_callback:
                progress_callback(50, "Generating HTML content...")
            
            # Generate HTML content
            html_content = self._generate_html_report(client_profile, assumptions, financial_results)
            
            if progress_callback:
                progress_callback(90, "Writing HTML file...")
            
            # Write HTML file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            if progress_callback:
                progress_callback(100, "HTML export completed")
            
            self.logger.info(f"HTML report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting HTML report: {str(e)}")
            raise
    
    def export_json_data(self,
                        client_profile: ClientProfile,
                        assumptions: EnhancedProjectAssumptions,
                        financial_results: Dict[str, Any],
                        output_dir: Optional[Path] = None) -> Path:
        """Export data as JSON."""
        try:
            # Create output directory
            if output_dir is None:
                output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_data_{timestamp}.json"
            filepath = output_dir['data_dir'] / filename
            
            # Prepare data
            export_data = {
                'client_profile': client_profile.to_dict(),
                'assumptions': assumptions.to_dict(),
                'financial_results': self._serialize_financial_results(financial_results),
                'export_timestamp': datetime.now().isoformat(),
                'version': '2.0.0'
            }
            
            # Write JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"JSON data exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting JSON data: {str(e)}")
            raise
    
    def _add_metadata_sheet(self, filepath: Path, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions):
        """Add metadata sheet to Excel file."""
        try:
            # Read existing Excel file
            with pd.ExcelWriter(str(filepath), mode='a', engine='openpyxl') as writer:
                # Create metadata DataFrame
                metadata = {
                    'Parameter': [
                        'Client Company', 'Client Name', 'Project Name', 'Report Date',
                        'Consultant', 'Project Capacity (MW)', 'Project Life (years)',
                        'CAPEX (M EUR)', 'Total Grants (M EUR)', 'Grant Percentage (%)',
                        'Export Timestamp'
                    ],
                    'Value': [
                        client_profile.company_name,
                        client_profile.client_name,
                        client_profile.project_name,
                        client_profile.report_date,
                        client_profile.consultant,
                        assumptions.capacity_mw,
                        assumptions.project_life_years,
                        assumptions.capex_meur,
                        assumptions.calculate_total_grants(),
                        assumptions.calculate_grant_percentage(),
                        datetime.now().isoformat()
                    ]
                }
                
                metadata_df = pd.DataFrame(metadata)
                metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
                
        except Exception as e:
            self.logger.warning(f"Could not add metadata sheet: {str(e)}")
    
    def _add_docx_header(self, doc, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions):
        """Add header to DOCX document."""
        # Title
        title = doc.add_heading('Financial Model Report', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Client information
        doc.add_heading('Project Information', level=1)
        
        info_table = doc.add_table(rows=6, cols=2)
        info_table.style = 'Table Grid'
        
        info_data = [
            ('Client Company', client_profile.company_name),
            ('Project Name', client_profile.project_name),
            ('Capacity', f"{assumptions.capacity_mw} MW"),
            ('Location', assumptions.location_name),
            ('Report Date', client_profile.report_date),
            ('Consultant', client_profile.consultant)
        ]
        
        for i, (label, value) in enumerate(info_data):
            info_table.cell(i, 0).text = label
            info_table.cell(i, 1).text = str(value)
    
    def _add_docx_financial_results(self, doc, financial_results: Dict[str, Any]):
        """Add financial results to DOCX document."""
        doc.add_heading('Financial Results', level=1)
        
        kpis = financial_results.get('kpis', {})
        
        # KPI table
        kpi_table = doc.add_table(rows=7, cols=2)
        kpi_table.style = 'Table Grid'
        
        kpi_data = [
            ('Project IRR', f"{kpis.get('IRR_project', 0):.1%}"),
            ('Equity IRR', f"{kpis.get('IRR_equity', 0):.1%}"),
            ('NPV Project (M EUR)', f"{kpis.get('NPV_project', 0)/1e6:.2f}"),
            ('NPV Equity (M EUR)', f"{kpis.get('NPV_equity', 0)/1e6:.2f}"),
            ('LCOE (EUR/kWh)', f"{kpis.get('LCOE_eur_kwh', 0):.3f}"),
            ('Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}"),
            ('Payback Period (years)', f"{kpis.get('Payback_years', 0):.1f}")
        ]
        
        for i, (label, value) in enumerate(kpi_data):
            kpi_table.cell(i, 0).text = label
            kpi_table.cell(i, 1).text = value
    
    def _add_docx_charts(self, doc, charts: Dict[str, bytes]):
        """Add charts to DOCX document."""
        doc.add_heading('Charts and Analysis', level=1)
        
        for chart_name, chart_data in charts.items():
            doc.add_heading(chart_name.replace('_', ' ').title(), level=2)
            
            # Save chart as temporary file and add to document
            temp_path = Path(f"temp_{chart_name}.png")
            try:
                with open(temp_path, 'wb') as f:
                    f.write(chart_data)
                doc.add_picture(str(temp_path), width=Inches(6))
                temp_path.unlink()  # Delete temporary file
            except Exception as e:
                doc.add_paragraph(f"Chart could not be embedded: {str(e)}")
    
    def _add_docx_validation_results(self, doc, validation_results):
        """Add validation results to DOCX document."""
        doc.add_heading('Model Validation', level=1)
        
        # Validation status
        status = "PASSED" if validation_results.is_valid else "FAILED"
        doc.add_paragraph(f"Validation Status: {status}")
        
        # Warnings
        if validation_results.warnings:
            doc.add_heading('Warnings', level=2)
            for warning in validation_results.warnings:
                doc.add_paragraph(f"• {warning}")
        
        # Errors
        if validation_results.errors:
            doc.add_heading('Errors', level=2)
            for error in validation_results.errors:
                doc.add_paragraph(f"• {error}")
    
    def _generate_html_report(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions, financial_results: Dict[str, Any]) -> str:
        """Generate HTML report content."""
        kpis = financial_results.get('kpis', {})
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Financial Model Report - {client_profile.project_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .section {{ margin-bottom: 30px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .kpi-value {{ font-weight: bold; color: #2E8B57; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Financial Model Report</h1>
                <h2>{client_profile.project_name}</h2>
                <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h3>Project Information</h3>
                <table>
                    <tr><th>Parameter</th><th>Value</th></tr>
                    <tr><td>Client Company</td><td>{client_profile.company_name}</td></tr>
                    <tr><td>Project Name</td><td>{client_profile.project_name}</td></tr>
                    <tr><td>Capacity</td><td>{assumptions.capacity_mw} MW</td></tr>
                    <tr><td>Location</td><td>{assumptions.location_name}</td></tr>
                    <tr><td>CAPEX</td><td>{assumptions.capex_meur} M EUR</td></tr>
                </table>
            </div>
            
            <div class="section">
                <h3>Key Performance Indicators</h3>
                <table>
                    <tr><th>Metric</th><th>Value</th></tr>
                    <tr><td>Project IRR</td><td class="kpi-value">{kpis.get('IRR_project', 0):.1%}</td></tr>
                    <tr><td>Equity IRR</td><td class="kpi-value">{kpis.get('IRR_equity', 0):.1%}</td></tr>
                    <tr><td>NPV Project</td><td class="kpi-value">{kpis.get('NPV_project', 0)/1e6:.2f} M EUR</td></tr>
                    <tr><td>NPV Equity</td><td class="kpi-value">{kpis.get('NPV_equity', 0)/1e6:.2f} M EUR</td></tr>
                    <tr><td>LCOE</td><td class="kpi-value">{kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh</td></tr>
                    <tr><td>Min DSCR</td><td class="kpi-value">{kpis.get('Min_DSCR', 0):.2f}</td></tr>
                </table>
            </div>
            
            <div class="section">
                <h3>Report Information</h3>
                <p><strong>Consultant:</strong> {client_profile.consultant}</p>
                <p><strong>Website:</strong> {client_profile.consultant_website}</p>
                <p><strong>Tagline:</strong> {client_profile.tagline}</p>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def _serialize_financial_results(self, financial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize financial results for JSON export."""
        serialized = {}
        
        for key, value in financial_results.items():
            if isinstance(value, pd.DataFrame):
                serialized[key] = value.to_dict('records')
            elif isinstance(value, (pd.Series, pd.Index)):
                serialized[key] = value.to_list()
            else:
                serialized[key] = value
        
        return serialized
