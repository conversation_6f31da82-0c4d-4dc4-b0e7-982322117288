"""
Client Profile Data Model
=========================

Data model for client profile information and validation.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any
import re


@dataclass
class ClientProfile:
    """Client profile information for financial modeling projects."""
    
    company_name: str = ""
    client_name: str = ""
    contact_email: str = ""
    phone: str = ""
    project_name: str = ""
    report_date: str = field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"))
    consultant: str = "Agevolami SRL"
    consultant_website: str = "www.agevolami.it & www.agevolami.ma"
    tagline: str = "Your way to explore crossborder opportunities and grow big"
    
    # Additional fields for enhanced functionality
    industry_sector: str = "Renewable Energy"
    project_location: str = ""
    project_capacity_mw: Optional[float] = None
    preferred_currency: str = "EUR"
    language: str = "EN"
    
    def __post_init__(self):
        """Validate and clean data after initialization."""
        self.company_name = self.company_name.strip()
        self.client_name = self.client_name.strip()
        self.contact_email = self.contact_email.strip().lower()
        self.phone = self.phone.strip()
        self.project_name = self.project_name.strip()
    
    def validate(self) -> Dict[str, str]:
        """Validate client profile data and return validation errors."""
        errors = {}
        
        if not self.company_name:
            errors['company_name'] = "Company name is required"
        
        if not self.client_name:
            errors['client_name'] = "Client name is required"
        
        if self.contact_email and not self._is_valid_email(self.contact_email):
            errors['contact_email'] = "Invalid email format"
        
        if self.phone and not self._is_valid_phone(self.phone):
            errors['phone'] = "Invalid phone format"
        
        if not self.project_name:
            errors['project_name'] = "Project name is required"
        
        return errors
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone format (basic validation)."""
        # Remove common separators
        clean_phone = re.sub(r'[\s\-\(\)\+]', '', phone)
        # Check if it contains only digits and is reasonable length
        return clean_phone.isdigit() and 7 <= len(clean_phone) <= 15
    
    def get_clean_company_name(self) -> str:
        """Get filesystem-safe company name."""
        if not self.company_name:
            return "Client"
        
        # Remove special characters and replace spaces with underscores
        clean_name = re.sub(r'[<>:"/\\|?*]', '', self.company_name)
        clean_name = re.sub(r'\s+', '_', clean_name)
        return clean_name
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'company_name': self.company_name,
            'client_name': self.client_name,
            'contact_email': self.contact_email,
            'phone': self.phone,
            'project_name': self.project_name,
            'report_date': self.report_date,
            'consultant': self.consultant,
            'consultant_website': self.consultant_website,
            'tagline': self.tagline,
            'industry_sector': self.industry_sector,
            'project_location': self.project_location,
            'project_capacity_mw': self.project_capacity_mw,
            'preferred_currency': self.preferred_currency,
            'language': self.language
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClientProfile':
        """Create instance from dictionary."""
        return cls(**{k: v for k, v in data.items() if k in cls.__dataclass_fields__})
    
    def is_complete(self) -> bool:
        """Check if profile has minimum required information."""
        return bool(self.company_name and self.client_name and self.project_name)
    
    def get_display_name(self) -> str:
        """Get display name for UI."""
        if self.company_name and self.client_name:
            return f"{self.company_name} - {self.client_name}"
        elif self.company_name:
            return self.company_name
        elif self.client_name:
            return self.client_name
        else:
            return "Unnamed Client"
