"""
Scenarios Analysis View
=======================

View component for scenario analysis results.
"""

import flet as ft
from typing import Dict, Any, Optional, List

from .base_view import BaseView


class ScenariosView(BaseView):
    """View for scenario analysis results."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.scenario_results: Optional[Dict[str, Any]] = None
        self.selected_scenarios = ["Base", "Optimistic", "Pessimistic"]
    
    def build_content(self) -> ft.Control:
        """Build the scenarios view content."""
        
        # Header
        header = self.create_section_header(
            "Scenario Analysis",
            "Compare different project scenarios and assumptions"
        )
        
        # Scenario selection
        scenario_selection = self._create_scenario_selection()
        
        # Results display
        if self.scenario_results:
            results_content = self._create_results_display()
        else:
            results_content = self.create_empty_state(
                "No Scenario Results",
                "Run scenario analysis to compare different cases",
                "Run Analysis",
                self._on_run_analysis
            )
        
        return ft.Column([
            header,
            scenario_selection,
            results_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_scenario_selection(self) -> ft.Card:
        """Create scenario selection interface."""
        available_scenarios = [
            "Base", "Optimistic", "Pessimistic", "High_Grants", "No_Grants"
        ]
        
        scenario_checkboxes = []
        for scenario in available_scenarios:
            checkbox = ft.Checkbox(
                label=scenario.replace('_', ' '),
                value=scenario in self.selected_scenarios,
                on_change=lambda e, s=scenario: self._on_scenario_selected(s, e.control.value)
            )
            scenario_checkboxes.append(checkbox)
        
        selection_content = ft.Column([
            ft.Text("Select Scenarios to Analyze:", size=16, weight=ft.FontWeight.BOLD),
            ft.Column(scenario_checkboxes),
            ft.Divider(height=20),
            self.create_action_button(
                "Run Scenario Analysis",
                ft.Icons.COMPARE,
                self._on_run_analysis,
                ft.Colors.BLUE_600
            )
        ])
        
        return self.create_card(
            "Scenario Selection",
            selection_content,
            icon=ft.Icons.PLAYLIST_ADD_CHECK
        )
    
    def _create_results_display(self) -> ft.Card:
        """Create scenario results display."""
        # Placeholder for scenario results
        placeholder = ft.Container(
            content=ft.Text("Scenario analysis results and comparison charts would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=400,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
        
        return self.create_card(
            "Scenario Analysis Results",
            placeholder,
            icon=ft.Icons.COMPARE_ARROWS
        )
    
    def _on_scenario_selected(self, scenario: str, selected: bool):
        """Handle scenario selection change."""
        if selected and scenario not in self.selected_scenarios:
            self.selected_scenarios.append(scenario)
        elif not selected and scenario in self.selected_scenarios:
            self.selected_scenarios.remove(scenario)
    
    def _on_run_analysis(self, e=None):
        """Run scenario analysis."""
        if len(self.selected_scenarios) < 1:
            self.show_error("Please select at least one scenario for analysis")
            return
        
        self.request_action("run_scenario_analysis", {
            "scenarios": self.selected_scenarios
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with scenario results."""
        if "scenario_results" in data:
            self.scenario_results = data["scenario_results"]
            self.refresh()
    
    def set_scenario_results(self, results: Dict[str, Any]):
        """Set scenario results."""
        self.scenario_results = results
        self.refresh()
