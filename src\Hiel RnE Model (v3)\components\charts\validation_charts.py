"""
Validation Charts
=================

Chart components for model validation and benchmarking visualization.
"""

import flet as ft
from typing import Dict, Any, Optional, List

from .chart_factory import ChartFactory


class ValidationCharts:
    """Chart components for validation visualization."""
    
    def __init__(self):
        self.chart_factory = ChartFactory()
    
    def create_validation_status_chart(self, validation_results: Any) -> ft.Container:
        """Create validation status visualization."""
        if not validation_results:
            return ft.Container()
        
        is_valid = getattr(validation_results, 'is_valid', False)
        warnings = getattr(validation_results, 'warnings', [])
        errors = getattr(validation_results, 'errors', [])
        
        # Create status summary
        status_data = {
            "Passed": 1 if is_valid else 0,
            "Warnings": len(warnings),
            "Errors": len(errors)
        }
        
        return self.chart_factory.create_pie_chart(
            status_data,
            "Model Validation Status"
        )
    
    def create_benchmark_comparison_chart(self, benchmark_results: Dict[str, Any]) -> ft.Container:
        """Create benchmark comparison visualization."""
        if not benchmark_results:
            return ft.Container()
        
        custom_analysis = benchmark_results.get('custom_analysis', {})
        
        if not custom_analysis:
            return ft.Container(
                content=ft.Text("No benchmark data available"),
                alignment=ft.alignment.center
            )
        
        # Create comparison for key metrics
        comparison_content = ft.Column([
            ft.Text("Benchmark Comparison", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10)
        ])
        
        key_metrics = ['irr_project', 'irr_equity', 'dscr', 'capacity_factor']
        
        for metric in key_metrics:
            if metric in custom_analysis:
                metric_data = custom_analysis[metric]
                current = metric_data.get('current', 0)
                benchmark = metric_data.get('benchmark_min', 0) or metric_data.get('benchmark_target', 0)
                status = metric_data.get('status', 'unknown')
                
                # Create visual comparison
                metric_row = ft.Row([
                    ft.Text(metric.replace('_', ' ').title(), size=12, expand=2),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                width=max(10, current * 100 if current < 1 else current * 10),
                                height=15,
                                bgcolor=self._get_status_color(status),
                                border_radius=3
                            ),
                            ft.Text(f"{current:.2f}", size=10)
                        ]),
                        expand=2
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                width=max(10, benchmark * 100 if benchmark < 1 else benchmark * 10),
                                height=10,
                                bgcolor=ft.Colors.GREY_400,
                                border_radius=3
                            ),
                            ft.Text(f"{benchmark:.2f}", size=8, color=ft.Colors.GREY_600)
                        ]),
                        expand=2
                    ),
                    ft.Icon(
                        self._get_status_icon(status),
                        color=self._get_status_color(status),
                        size=16
                    )
                ])
                
                comparison_content.controls.append(metric_row)
        
        return ft.Container(
            content=comparison_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _get_status_color(self, status: str) -> str:
        """Get color based on status."""
        color_map = {
            'excellent': ft.Colors.GREEN,
            'good': ft.Colors.BLUE,
            'acceptable': ft.Colors.ORANGE,
            'poor': ft.Colors.RED,
            'warning': ft.Colors.ORANGE
        }
        return color_map.get(status, ft.Colors.GREY)
    
    def _get_status_icon(self, status: str) -> str:
        """Get icon based on status."""
        icon_map = {
            'excellent': ft.Icons.CHECK_CIRCLE,
            'good': ft.Icons.CHECK,
            'acceptable': ft.Icons.WARNING,
            'poor': ft.Icons.ERROR,
            'warning': ft.Icons.WARNING
        }
        return icon_map.get(status, ft.Icons.HELP)
    
    def create_validation_issues_chart(self, validation_results: Any) -> ft.Container:
        """Create validation issues visualization."""
        if not validation_results:
            return ft.Container()
        
        warnings = getattr(validation_results, 'warnings', [])
        errors = getattr(validation_results, 'errors', [])
        
        issues_content = ft.Column([
            ft.Text("Validation Issues", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10)
        ])
        
        # Show errors
        if errors:
            issues_content.controls.append(
                ft.Text("Errors:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.RED)
            )
            for error in errors[:5]:  # Show first 5
                issues_content.controls.append(
                    ft.Row([
                        ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED, size=16),
                        ft.Text(str(error), size=12, color=ft.Colors.RED_700, expand=True)
                    ])
                )
        
        # Show warnings
        if warnings:
            if errors:
                issues_content.controls.append(ft.Divider(height=10))
            
            issues_content.controls.append(
                ft.Text("Warnings:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE)
            )
            for warning in warnings[:5]:  # Show first 5
                issues_content.controls.append(
                    ft.Row([
                        ft.Icon(ft.Icons.WARNING, color=ft.Colors.ORANGE, size=16),
                        ft.Text(str(warning), size=12, color=ft.Colors.ORANGE_700, expand=True)
                    ])
                )
        
        if not errors and not warnings:
            issues_content.controls.append(
                ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=20),
                    ft.Text("No validation issues found", 
                           size=14, color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD)
                ], alignment=ft.MainAxisAlignment.CENTER)
            )
        
        return ft.Container(
            content=issues_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def create_risk_assessment_chart(self, benchmark_results: Dict[str, Any]) -> ft.Container:
        """Create risk assessment visualization."""
        if not benchmark_results:
            return ft.Container()
        
        # Placeholder for risk assessment
        return ft.Container(
            content=ft.Text("Risk assessment chart would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
