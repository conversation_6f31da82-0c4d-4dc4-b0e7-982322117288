# Enhanced Financial Model - Developer Guide

## Architecture Overview

The Enhanced Financial Model application follows a modular, layered architecture designed for maintainability, testability, and extensibility.

### Architecture Layers

```
┌─────────────────────────────────────────┐
│                UI Layer                 │
│  (Views, Components, Controllers)       │
├─────────────────────────────────────────┤
│              Service Layer              │
│  (Business Logic, Analysis Services)    │
├─────────────────────────────────────────┤
│               Model Layer               │
│    (Data Models, State Management)      │
├─────────────────────────────────────────┤
│              Utility Layer              │
│   (File Utils, Validation, Formatting)  │
└─────────────────────────────────────────┘
```

### Key Design Principles

1. **Separation of Concerns**: Clear boundaries between UI, business logic, and data
2. **Dependency Injection**: Services injected into controllers and views
3. **Event-Driven Architecture**: Loose coupling through callbacks and events
4. **Modular Design**: Self-contained components with well-defined interfaces
5. **Testability**: Comprehensive unit and integration test coverage

## Project Structure

```
src/new_app/
├── app/                    # Application core
│   ├── app_controller.py   # Main application controller
│   └── app_state.py        # Centralized state management
├── models/                 # Data models
│   ├── client_profile.py   # Client information model
│   ├── project_assumptions.py # Project parameters model
│   ├── location_config.py  # Location configuration
│   └── ui_state.py         # UI state management
├── services/               # Business logic services
│   ├── financial_service.py # Financial modeling service
│   ├── validation_service.py # Model validation service
│   ├── export_service.py   # Export functionality
│   ├── location_service.py # Location comparison service
│   └── report_service.py   # Report generation service
├── views/                  # UI view components
│   ├── base_view.py        # Base view class
│   ├── project_setup_view.py # Project setup interface
│   ├── dashboard_view.py   # Main dashboard
│   └── ...                # Other view components
├── components/             # Reusable UI components
│   ├── charts/             # Chart components
│   ├── forms/              # Form components
│   ├── widgets/            # UI widgets
│   └── ui/                 # Advanced UI components
├── config/                 # Configuration
│   ├── app_config.py       # Application configuration
│   ├── ui_config.py        # UI configuration
│   └── export_config.py    # Export configuration
├── utils/                  # Utility functions
│   ├── file_utils.py       # File operations
│   ├── validation_utils.py # Data validation
│   └── formatting_utils.py # Data formatting
├── tests/                  # Test suite
│   ├── test_models.py      # Model tests
│   ├── test_services.py    # Service tests
│   ├── test_utils.py       # Utility tests
│   └── test_integration.py # Integration tests
└── main.py                 # Application entry point
```

## Core Components

### Application Controller

The `AppController` is the main orchestrator:

```python
class AppController:
    def __init__(self, page: ft.Page):
        self.page = page
        self.app_state = AppState()
        self.services = self._initialize_services()
        self.views = self._initialize_views()
    
    def handle_action_request(self, action: str, params: Dict[str, Any]):
        # Route actions to appropriate services
        pass
```

**Responsibilities:**
- Coordinate between views and services
- Manage application state
- Handle user actions and navigation
- Orchestrate complex workflows

### State Management

Centralized state management through `AppState`:

```python
@dataclass
class AppState:
    client_profile: ClientProfile
    project_assumptions: EnhancedProjectAssumptions
    financial_results: Optional[Dict[str, Any]]
    # ... other state properties
```

**Benefits:**
- Single source of truth
- Predictable state updates
- Easy debugging and testing
- Serialization support

### Service Layer

Services encapsulate business logic:

```python
class FinancialModelService:
    def run_financial_model(self, assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        # Core financial modeling logic
        pass
    
    def run_sensitivity_analysis(self, assumptions, variables) -> pd.DataFrame:
        # Sensitivity analysis logic
        pass
```

**Service Types:**
- **FinancialModelService**: Core financial calculations
- **ValidationService**: Model validation and benchmarking
- **ExportService**: Report generation and export
- **LocationService**: Location comparison analysis
- **ReportService**: Comprehensive report generation

### View Components

Views handle UI presentation and user interaction:

```python
class BaseView:
    def __init__(self, page: ft.Page):
        self.page = page
        self.on_navigate: Optional[Callable] = None
        self.on_data_changed: Optional[Callable] = None
        self.on_action_requested: Optional[Callable] = None
    
    def build_content(self) -> ft.Control:
        # Abstract method for building view content
        pass
```

**View Hierarchy:**
- **BaseView**: Common functionality for all views
- **Specialized Views**: Dashboard, Setup, Analysis views
- **Form Components**: Reusable form elements
- **Chart Components**: Visualization components

## Data Models

### Model Design

Models use dataclasses with validation:

```python
@dataclass
class EnhancedProjectAssumptions:
    capacity_mw: float = 10.0
    production_mwh_year1: float = 18000.0
    # ... other fields
    
    def validate_all(self) -> Dict[str, str]:
        # Comprehensive validation logic
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        # Serialization support
        pass
```

**Model Features:**
- Type hints for better IDE support
- Validation methods
- Serialization/deserialization
- Default values
- Calculated properties

### State Models

UI state management:

```python
@dataclass
class UIState:
    current_tab: TabState = TabState.PROJECT_SETUP
    is_loading: bool = False
    progress: float = 0.0
    error_message: str = ""
    
    def can_navigate_to_tab(self, tab: TabState) -> bool:
        # Navigation logic based on current state
        pass
```

## Service Implementation

### Financial Service

Core financial modeling service:

```python
class FinancialModelService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def run_financial_model(self, assumptions: EnhancedProjectAssumptions, 
                           progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Run comprehensive financial model."""
        
        # Validate inputs
        if not assumptions.is_validated:
            raise ValueError("Assumptions must be validated first")
        
        # Progress tracking
        if progress_callback:
            progress_callback(10, "Initializing model...")
        
        # Core calculations
        cashflow = self._calculate_cashflow(assumptions)
        kpis = self._calculate_kpis(cashflow, assumptions)
        
        if progress_callback:
            progress_callback(100, "Model completed")
        
        return {
            'kpis': kpis,
            'cashflow': cashflow,
            'assumptions': assumptions.to_dict()
        }
```

### Validation Service

Model validation and benchmarking:

```python
class ValidationService:
    def validate_model(self, assumptions, kpis, cashflow) -> ValidationResult:
        """Comprehensive model validation."""
        
        result = ValidationResult()
        
        # Validate assumptions
        assumption_errors = self._validate_assumptions(assumptions)
        result.errors.extend(assumption_errors)
        
        # Validate KPIs
        kpi_warnings = self._validate_kpis(kpis)
        result.warnings.extend(kpi_warnings)
        
        # Validate cashflow
        cashflow_issues = self._validate_cashflow(cashflow)
        result.errors.extend(cashflow_issues)
        
        result.is_valid = len(result.errors) == 0
        return result
```

## UI Components

### Chart Components

Reusable chart components using matplotlib:

```python
class ChartFactory:
    def create_bar_chart(self, data: Dict[str, float], title: str) -> ft.Container:
        """Create a bar chart component."""
        
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # Plot data
        categories = list(data.keys())
        values = list(data.values())
        ax.bar(categories, values)
        
        # Convert to Flet image
        img_base64 = self._fig_to_base64(fig)
        plt.close(fig)
        
        return ft.Container(
            content=ft.Image(src_base64=img_base64),
            alignment=ft.alignment.center
        )
```

### Form Components

Reusable form components:

```python
class ClientProfileForm:
    def __init__(self, client_profile: ClientProfile):
        self.client_profile = client_profile
        self.on_data_changed: Optional[Callable] = None
    
    def build(self) -> ft.Container:
        """Build the form UI."""
        return ft.Container(
            content=ft.Column([
                self._create_company_name_field(),
                self._create_client_name_field(),
                # ... other fields
            ])
        )
```

## Testing Strategy

### Test Structure

```
tests/
├── test_models.py          # Unit tests for data models
├── test_services.py        # Unit tests for services
├── test_utils.py           # Unit tests for utilities
├── test_integration.py     # Integration tests
├── conftest.py            # Test configuration and fixtures
└── run_tests.py           # Test runner script
```

### Test Categories

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **UI Tests**: Test user interface components
4. **Performance Tests**: Test with large datasets

### Running Tests

```bash
# Run all tests
python src/new_app/tests/run_tests.py

# Run specific test types
python src/new_app/tests/run_tests.py --type unit
python src/new_app/tests/run_tests.py --type integration

# Run with coverage
python src/new_app/tests/run_tests.py --coverage
```

## Extension Points

### Adding New Analysis Types

1. **Create Service Method**:
   ```python
   def run_new_analysis(self, assumptions, parameters):
       # Analysis logic
       return results
   ```

2. **Add View Component**:
   ```python
   class NewAnalysisView(BaseView):
       def build_content(self) -> ft.Control:
           # UI for new analysis
           pass
   ```

3. **Update Controller**:
   ```python
   def handle_action_request(self, action, params):
       if action == "run_new_analysis":
           results = self.service.run_new_analysis(...)
   ```

### Adding New Export Formats

1. **Extend Export Service**:
   ```python
   def export_new_format(self, client_profile, assumptions, results):
       # Export logic for new format
       pass
   ```

2. **Update Export Configuration**:
   ```python
   default_formats = ["Excel", "DOCX", "HTML", "JSON", "NewFormat"]
   ```

### Adding New Locations

1. **Update Location Configuration**:
   ```python
   MOROCCO_LOCATIONS = {
       "new_location": {
           "name": "New Location",
           "irradiation": 2100,
           "capacity_factor": 0.22
       }
   }
   ```

## Performance Considerations

### Memory Management

- Use generators for large datasets
- Clear matplotlib figures after use
- Implement data pagination for large results

### Computation Optimization

- Cache expensive calculations
- Use vectorized operations with pandas/numpy
- Implement progress callbacks for long operations

### UI Performance

- Lazy load chart components
- Use virtual scrolling for large lists
- Debounce user input validation

## Security Considerations

### Data Protection

- All data stored locally
- No external API calls
- Secure file permissions

### Input Validation

- Comprehensive parameter validation
- SQL injection prevention (if database added)
- File path sanitization

## Deployment and Distribution

### Packaging Options

1. **Source Distribution**: Python source code
2. **Wheel Distribution**: Binary distribution
3. **Executable**: PyInstaller or similar
4. **Container**: Docker image

### Configuration Management

- Environment-specific configurations
- Secure credential storage
- Feature flags for different deployments

## Contributing Guidelines

### Code Style

- Follow PEP 8 style guidelines
- Use type hints consistently
- Document all public methods
- Write comprehensive tests

### Pull Request Process

1. Create feature branch
2. Implement changes with tests
3. Update documentation
4. Submit pull request
5. Code review and approval

### Issue Reporting

- Use issue templates
- Provide reproduction steps
- Include system information
- Attach relevant logs

---

**Author**: Abdelhalim Serhani, Agevolami SRL  
**Version**: 2.0.0  
**Last Updated**: 2024
