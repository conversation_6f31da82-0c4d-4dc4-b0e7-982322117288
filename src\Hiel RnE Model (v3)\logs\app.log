2025-06-16 10:23:32,213 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 10:23:32,213 - root - INFO - Author: <PERSON><PERSON><PERSON><PERSON>
2025-06-16 10:23:32,214 - root - INFO - Company: Agevolami SRL
2025-06-16 10:23:32,214 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 10:23:32,216 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\Hiel RnE Model (v3)\assets
2025-06-16 10:23:32,222 - flet - INFO - Starting up TCP server on localhost:14914
2025-06-16 10:23:32,229 - flet - INFO - Flet app has started...
2025-06-16 10:23:32,232 - flet - INFO - App URL: tcp://localhost:14914
2025-06-16 10:23:32,232 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 10:23:32,236 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:23:32,236 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:23:32,441 - flet - INFO - App session started
2025-06-16 10:23:37,215 - app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 10:23:37,216 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 10:23:39,681 - app.app_controller - INFO - Navigated to tab: location_comparison
2025-06-16 10:23:49,936 - app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 10:23:51,688 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:51,872 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:51,974 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:52,151 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:53,125 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:53,280 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:53,911 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:54,078 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:54,234 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:54,313 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:54,429 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:54,606 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:55,178 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:57,002 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:57,197 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:57,295 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:57,466 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:58,518 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:58,705 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:58,777 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:58,955 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:59,017 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:59,162 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:23:59,261 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:00,001 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:02,991 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:03,260 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:03,858 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:04,019 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:04,179 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:04,709 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:05,500 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:05,913 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:06,123 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:06,251 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:07,043 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:07,185 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:07,458 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:07,839 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:24:10,593 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:10,836 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:11,133 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:15,728 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:15,941 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:16,215 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:19,566 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:19,775 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:22,842 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:30,099 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:30,340 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:24:34,329 - app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 10:24:34,354 - utils.file_utils - INFO - Created output directory structure: outputs\2025-06-16\Demo_Company0
2025-06-16 10:24:34,381 - services.financial_service - INFO - Financial model executed successfully
2025-06-16 10:24:34,390 - services.validation_service - INFO - Starting comprehensive model validation
2025-06-16 10:24:34,390 - services.validation_service - INFO - Validation completed - Valid: False
2025-06-16 10:24:34,396 - services.validation_service - INFO - Generating benchmark comparison
2025-06-16 10:24:34,397 - services.validation_service - INFO - Benchmark comparison completed
2025-06-16 10:24:34,412 - services.location_service - ERROR - Error in location comparison: EnhancedProjectAssumptions.__init__() got an unexpected keyword argument 'total_grant_meur_maroc'
2025-06-16 10:24:34,413 - services.report_service - ERROR - Error generating comprehensive report: EnhancedProjectAssumptions.__init__() got an unexpected keyword argument 'total_grant_meur_maroc'
2025-06-16 10:27:46,894 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 10:27:46,894 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 10:27:46,894 - root - INFO - Company: Agevolami SRL
2025-06-16 10:27:46,894 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 10:27:46,895 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\Hiel RnE Model (v3)\assets
2025-06-16 10:27:46,897 - flet - INFO - Starting up TCP server on localhost:1062
2025-06-16 10:27:46,904 - flet - INFO - Flet app has started...
2025-06-16 10:27:46,906 - flet - INFO - App URL: tcp://localhost:1062
2025-06-16 10:27:46,906 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 10:27:46,907 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:27:46,907 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:27:47,087 - flet - INFO - App session started
2025-06-16 10:27:53,239 - app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 10:27:53,240 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 10:27:55,207 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:55,408 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:55,514 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:55,693 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:56,117 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:56,466 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:56,708 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:57,572 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:58,157 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:58,357 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:58,501 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:58,600 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:58,706 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:58,892 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:27:59,394 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:00,704 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:00,917 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:00,994 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:01,589 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:01,933 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,133 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,341 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,529 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,563 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,733 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,812 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:02,990 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:06,525 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:06,717 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:07,011 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:07,202 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:07,409 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:09,620 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:09,897 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:10,239 - app.app_controller - INFO - Data changed: client_profile
2025-06-16 10:28:14,385 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:14,690 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:15,190 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:16,777 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:17,042 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:19,637 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:19,897 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:21,857 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:22,078 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:22,289 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:28:23,888 - app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 10:28:23,902 - utils.file_utils - INFO - Created output directory structure: outputs\2025-06-16\Demo_Company1
2025-06-16 10:28:23,930 - services.financial_service - INFO - Financial model executed successfully
2025-06-16 10:28:23,937 - services.validation_service - INFO - Starting comprehensive model validation
2025-06-16 10:28:23,940 - services.validation_service - INFO - Validation completed - Valid: False
2025-06-16 10:28:23,945 - services.validation_service - INFO - Generating benchmark comparison
2025-06-16 10:28:23,945 - services.validation_service - INFO - Benchmark comparison completed
2025-06-16 10:28:23,951 - services.location_service - ERROR - Error in location comparison: EnhancedProjectAssumptions.__init__() got an unexpected keyword argument 'total_grants_meur'
2025-06-16 10:28:23,952 - services.report_service - ERROR - Error generating comprehensive report: EnhancedProjectAssumptions.__init__() got an unexpected keyword argument 'total_grants_meur'
2025-06-16 10:32:59,086 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 10:32:59,086 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 10:32:59,086 - root - INFO - Company: Agevolami SRL
2025-06-16 10:32:59,086 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 10:32:59,087 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\Hiel RnE Model (v3)\assets
2025-06-16 10:32:59,092 - flet - INFO - Starting up TCP server on localhost:1229
2025-06-16 10:32:59,100 - flet - INFO - Flet app has started...
2025-06-16 10:32:59,103 - flet - INFO - App URL: tcp://localhost:1229
2025-06-16 10:32:59,103 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 10:32:59,106 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:32:59,107 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:32:59,303 - flet - INFO - App session started
2025-06-16 10:35:14,188 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 10:35:14,189 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 10:35:14,189 - root - INFO - Company: Agevolami SRL
2025-06-16 10:35:14,189 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 10:35:14,190 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\Hiel RnE Model (v3)\assets
2025-06-16 10:35:14,194 - flet - INFO - Starting up TCP server on localhost:1332
2025-06-16 10:35:14,205 - flet - INFO - Flet app has started...
2025-06-16 10:35:14,208 - flet - INFO - App URL: tcp://localhost:1332
2025-06-16 10:35:14,208 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 10:35:14,210 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:35:14,211 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 10:35:14,393 - flet - INFO - App session started
2025-06-16 10:35:26,973 - app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 10:35:26,974 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 10:35:31,731 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:35:31,996 - app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 10:35:32,252 - app.app_controller - INFO - Data changed: project_assumptions
