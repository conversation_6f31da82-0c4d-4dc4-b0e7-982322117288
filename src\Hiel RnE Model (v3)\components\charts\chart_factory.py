"""
Chart Factory
=============

Factory class for creating various chart components.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Set backend for thread-safe operation
import io
import base64


class ChartFactory:
    """Factory for creating chart components."""
    
    def __init__(self):
        self.default_colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e',
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#343a40'
        }
    
    def create_kpi_gauge(self, title: str, current_value: float, 
                        target_value: float, max_value: float,
                        unit: str = "", color: str = None) -> ft.Container:
        """Create a KPI gauge chart."""
        if color is None:
            color = self.default_colors['primary']
        
        # Calculate progress
        progress = min(current_value / max_value, 1.0) if max_value > 0 else 0
        target_progress = min(target_value / max_value, 1.0) if max_value > 0 else 0
        
        # Determine status color
        if current_value >= target_value:
            status_color = self.default_colors['success']
        elif current_value >= target_value * 0.8:
            status_color = self.default_colors['warning']
        else:
            status_color = self.default_colors['danger']
        
        gauge_content = ft.Column([
            ft.Text(title, size=14, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
            ft.Stack([
                ft.ProgressRing(
                    value=progress,
                    stroke_width=8,
                    color=status_color,
                    width=80,
                    height=80
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Text(f"{current_value:.1f}{unit}", 
                               size=12, weight=ft.FontWeight.BOLD,
                               text_align=ft.TextAlign.CENTER),
                        ft.Text(f"Target: {target_value:.1f}{unit}", 
                               size=8, color=ft.Colors.GREY_600,
                               text_align=ft.TextAlign.CENTER)
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    width=80,
                    height=80,
                    alignment=ft.alignment.center
                )
            ])
        ], alignment=ft.MainAxisAlignment.CENTER, 
           horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        
        return ft.Container(
            content=gauge_content,
            width=120,
            height=140,
            padding=10,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def create_bar_chart(self, data: Dict[str, float], title: str,
                        x_label: str = "", y_label: str = "",
                        color: str = None) -> ft.Container:
        """Create a bar chart."""
        if color is None:
            color = self.default_colors['primary']
        
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 6))
        
        categories = list(data.keys())
        values = list(data.values())
        
        bars = ax.bar(categories, values, color=color, alpha=0.7)
        
        # Customize chart
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel(x_label)
        ax.set_ylabel(y_label)
        ax.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.2f}', ha='center', va='bottom')
        
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        # Convert to image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        
        return ft.Container(
            content=ft.Image(
                src_base64=img_base64,
                width=400,
                height=300,
                fit=ft.ImageFit.CONTAIN
            ),
            alignment=ft.alignment.center
        )
    
    def create_line_chart(self, data: pd.DataFrame, title: str,
                         x_column: str, y_columns: List[str],
                         x_label: str = "", y_label: str = "") -> ft.Container:
        """Create a line chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(10, 6))
        
        colors = [self.default_colors['primary'], self.default_colors['secondary'],
                 self.default_colors['success'], self.default_colors['danger']]
        
        for i, column in enumerate(y_columns):
            if column in data.columns:
                color = colors[i % len(colors)]
                ax.plot(data[x_column], data[column], 
                       label=column.replace('_', ' ').title(),
                       color=color, linewidth=2, marker='o', markersize=4)
        
        # Customize chart
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel(x_label)
        ax.set_ylabel(y_label)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        
        # Convert to image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        
        return ft.Container(
            content=ft.Image(
                src_base64=img_base64,
                width=500,
                height=350,
                fit=ft.ImageFit.CONTAIN
            ),
            alignment=ft.alignment.center
        )
    
    def create_pie_chart(self, data: Dict[str, float], title: str) -> ft.Container:
        """Create a pie chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 8))
        
        labels = list(data.keys())
        sizes = list(data.values())
        colors = plt.cm.Set3(range(len(labels)))
        
        # Create pie chart
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                         autopct='%1.1f%%', startangle=90,
                                         explode=[0.05] * len(labels))
        
        # Customize chart
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # Make percentage text bold
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        plt.tight_layout()
        
        # Convert to image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        
        return ft.Container(
            content=ft.Image(
                src_base64=img_base64,
                width=400,
                height=400,
                fit=ft.ImageFit.CONTAIN
            ),
            alignment=ft.alignment.center
        )
    
    def create_waterfall_chart(self, categories: List[str], values: List[float],
                              title: str) -> ft.Container:
        """Create a waterfall chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate cumulative values
        cumulative = [0]
        for value in values[:-1]:  # Exclude last value (total)
            cumulative.append(cumulative[-1] + value)
        
        # Colors for positive/negative values
        colors = ['green' if v >= 0 else 'red' for v in values[:-1]]
        colors.append('blue')  # Total bar color
        
        # Create bars
        for i, (cat, val, cum) in enumerate(zip(categories, values, cumulative)):
            if i == len(categories) - 1:  # Total bar
                ax.bar(cat, val, bottom=0, color=colors[i], alpha=0.7)
            else:
                ax.bar(cat, val, bottom=cum, color=colors[i], alpha=0.7)
                
                # Add connecting lines
                if i < len(categories) - 2:
                    ax.plot([i + 0.4, i + 1.4], [cum + val, cum + val], 
                           'k--', alpha=0.5)
        
        # Customize chart
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linewidth=0.8)
        
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        # Convert to image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        
        return ft.Container(
            content=ft.Image(
                src_base64=img_base64,
                width=500,
                height=350,
                fit=ft.ImageFit.CONTAIN
            ),
            alignment=ft.alignment.center
        )
    
    def create_comparison_table(self, data: List[Dict[str, Any]], 
                               title: str) -> ft.Container:
        """Create a comparison table."""
        if not data:
            return ft.Container(
                content=ft.Text("No data available"),
                alignment=ft.alignment.center
            )
        
        # Get column headers
        headers = list(data[0].keys())
        
        # Create table columns
        columns = [ft.DataColumn(ft.Text(header.replace('_', ' ').title())) 
                  for header in headers]
        
        # Create table rows
        rows = []
        for row_data in data:
            cells = []
            for header in headers:
                value = row_data.get(header, '')
                if isinstance(value, float):
                    if abs(value) < 1:
                        text = f"{value:.3f}"
                    else:
                        text = f"{value:.2f}"
                else:
                    text = str(value)
                cells.append(ft.DataCell(ft.Text(text)))
            rows.append(ft.DataRow(cells=cells))
        
        table = ft.DataTable(
            columns=columns,
            rows=rows,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            vertical_lines=ft.border.BorderSide(1, ft.Colors.GREY_300),
            horizontal_lines=ft.border.BorderSide(1, ft.Colors.GREY_300)
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=16, weight=ft.FontWeight.BOLD),
                ft.Container(content=table, padding=10)
            ]),
            padding=10,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
